<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="12dp">

    <!--   region  title -->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="USD" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTime"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvTitle"
        tools:text="18/12.2024 04:08:51" />
    <!--  endregion-->

    <!--   region  type -->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvType"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/type"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="@id/tvTitle"
        app:layout_constraintTop_toBottomOf="@id/tvTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTypeValue"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/tvType"
        app:layout_constraintEnd_toEndOf="@id/tvTime"
        app:layout_constraintTop_toTopOf="@id/tvType"
        tools:text="Realized PnL" />
    <!--  endregion-->

    <!--   region  type -->
    <!--    <androidx.constraintlayout.widget.Group-->
    <!--        android:id="@+id/groupSymbol"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:visibility="gone"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        app:constraint_referenced_ids="tvSymbol,tvSymbolValue" />-->

    <!--    <androidx.appcompat.widget.AppCompatTextView-->
    <!--        android:id="@+id/tvSymbol"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginTop="8dp"-->
    <!--        android:text="@string/symbol"-->
    <!--        android:textColor="?attr/color_ca61e1e1e_c99ffffff"-->
    <!--        android:textSize="14dp"-->
    <!--        app:layout_constraintStart_toStartOf="@id/tvType"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/tvType" />-->

    <!--    <androidx.appcompat.widget.AppCompatTextView-->
    <!--        android:id="@+id/tvSymbolValue"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:textColor="?attr/color_c1e1e1e_cebffffff"-->
    <!--        android:textSize="14dp"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/tvSymbol"-->
    <!--        app:layout_constraintEnd_toEndOf="@id/tvTime"-->
    <!--        app:layout_constraintTop_toTopOf="@id/tvSymbol"-->
    <!--        tools:text="BTCUSD" />-->
    <!--  endregion-->

    <!--   region  type -->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAmount"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="12dp"
        android:text="@string/amount"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="@id/tvType"
        app:layout_constraintTop_toBottomOf="@id/tvType" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAmountValue"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/c00c79c"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/tvAmount"
        app:layout_constraintEnd_toEndOf="@id/tvTime"
        app:layout_constraintTop_toTopOf="@id/tvAmount"
        tools:text="+100.10" />
    <!--  endregion-->


    <!--   region  type -->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAmount"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="12dp"
        android:text="@string/amount"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="@id/tvType"
        app:layout_constraintTop_toBottomOf="@id/tvType" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAmountValue"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/c00c79c"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/tvAmount"
        app:layout_constraintEnd_toEndOf="@id/tvTime"
        app:layout_constraintTop_toTopOf="@id/tvAmount"
        tools:text="+100.10" />
    <!--  endregion-->

    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:layout_marginTop="12dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvAmount" />

</androidx.constraintlayout.widget.ConstraintLayout>