package cn.com.vau.history.data

import android.text.SpannedString
import androidx.annotation.Keep
import cn.com.vau.data.BaseBean

/**
 * Create data：2025/1/21 13:32
 * @author: Brin
 * Describe:
 */
@Keep
data class FundingData(
    val order: String? = "",
//    val currencyName: String = "",
    val timeStr: String? = "",
    val typeName: String? = "",
    val symbol: String? = "",
    val amount: String? = "",
    val comment: String? = "",
)

@Keep
data class FundingUIData(
    val order: String = "",
    val currencyName: String = "",
    val timeStr: String = "",
    val typeTitle: String = "",
    val typeName: String = "",
    val symbolTitle: String = "",
    val symbol: String = "",
    val amountTitle: String = "",
    val amount: SpannedString = SpannedString(""),
    val comment: String = "",
)


@Keep
data class FundingWrapper(
    var list: ArrayList<FundingData>?
)

@Keep
data class FundingHistoryBean(
    var obj: FundingWrapper?
) : BaseBean()
