package cn.com.vau.history.ui

import android.app.Activity
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.databinding.FragmentFundingHistoryBinding
import cn.com.vau.databinding.VsLayoutNoDataBinding
import cn.com.vau.history.data.HistoryFundStatisticsBean
import cn.com.vau.history.data.uiStatus.HistoryStatus
import cn.com.vau.history.ui.adapter.HistoryFundingAdapter
import cn.com.vau.history.viewmodel.FundingHistoryViewModel
import cn.com.vau.trade.ext.setExpandAnimation
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.onClickWithDefaultDelegate
import cn.com.vau.util.setTextColorDiff
import cn.com.vau.util.setTextDiff
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Create data：2025/1/13 10:38
 * @author: Brin
 * Describe:
 */
class HistoryFundingFragment : BaseMvvmBindingFragment<FragmentFundingHistoryBinding>() {
    private val fundingViewModel: FundingHistoryViewModel by viewModels()
    private val positionHistoryAdapter: HistoryFundingAdapter by lazy {
        HistoryFundingAdapter(
            fundingViewModel,
        )
    }

    private val bottomTypesDialog: BottomFundingTypeDialog by lazy {
        BottomFundingTypeDialog
            .Builder(
                requireContext() as Activity,
                fundingViewModel,
            ).build()
    }
    private val bottomTimesDialog: BottomTimeDialog by lazy {
        BottomTimeDialog
            .Builder(
                requireContext() as Activity,
                fundingViewModel,
            ).build()
    }

    private var fundExpandState: FundExpandState = FundExpandState.Expanded

    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(requireActivity(), R.attr.color_c1e1e1e_cebffffff) }

    private val c00c79c by lazy { ContextCompat.getColor(requireActivity(), R.color.c00c79c) }

    private val ce35728 by lazy { ContextCompat.getColor(requireActivity(), R.color.ce35728) }

    override fun initView() {
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            fundingViewModel.refreshHistoryData()
        }
        mBinding.mSmartRefreshLayout.setOnLoadMoreListener {
            fundingViewModel.loadMoreHistoryData()
        }
        mBinding.mRecyclerView.adapter = positionHistoryAdapter
        mBinding.mRecyclerView.itemAnimator = null
        mBinding.tvTypes.setOnClickListener {
            bottomTypesDialog.showDialog()
        }
        mBinding.ivFilter.onClickWithDefaultDelegate {
            bottomTimesDialog.showDialog()
        }
        mBinding.tvTimePeriod.onClickWithDefaultDelegate {
            switchPnlExpandState()
        }
        mBinding.mEmptyView.setOnInflateListener { _, inflated ->
            val vs = VsLayoutNoDataBinding.bind(inflated)
            vs.mNoDataView.id = R.id.mEmptyView
            vs.mNoDataView.setBackgroundColor(
                AttrResourceUtil.getColor(
                    requireContext(),
                    R.attr.mainLayoutBg,
                ),
            )
            vs.mNoDataView.setIconResource(
                AttrResourceUtil.getDrawable(
                    requireContext(),
                    R.attr.icNoDataBase,
                ),
            )
            vs.mNoDataView.setHintMessage(getString(R.string.no_records_found))
        }
        mBinding.mErrorView.setOnInflateListener { _, inflated ->
            val vs = VsLayoutNoDataBinding.bind(inflated)
            vs.mNoDataView.id = R.id.mErrorView
            vs.mNoDataView.setBackgroundColor(
                AttrResourceUtil.getColor(
                    requireContext(),
                    R.attr.mainLayoutBg,
                ),
            )
            vs.mNoDataView.setIconResource(
                AttrResourceUtil.getDrawable(
                    requireContext(),
                    R.attr.icNoConnection,
                ),
            )
            vs.mNoDataView.setHintMessage("No internet connection or server error, please try again later.")
            vs.mNoDataView.setBottomBtnText("try again")
            vs.mNoDataView.setBottomBtnViewClickListener {
                fundingViewModel.refreshHistoryData()
            }
        }
    }

    override fun createObserver() {
        fundingViewModel.historyStatus.observe(viewLifecycleOwner) {
            when (it) {
                is HistoryStatus.Initial -> {
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = true
                }

                is HistoryStatus.Refreshing -> {
                    mBinding.mSmartRefreshLayout.autoRefreshAnimationOnly()
                    mBinding.mSmartRefreshLayout.resetNoMoreData()
                }

                is HistoryStatus.Empty -> {
                    mBinding.mSmartRefreshLayout.finishRefresh()
                    mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = true
                }

                is HistoryStatus.RefreshingEnd -> {
                    mBinding.mSmartRefreshLayout.finishRefresh()
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = false
                    mBinding.mSmartRefreshLayout.setEnableLoadMore(true)
                    mBinding.mRecyclerView.smoothScrollToPosition(0)
                }

                is HistoryStatus.RefreshingFailed -> {
                    mBinding.mSmartRefreshLayout.finishRefresh(false)
                    mBinding.mErrorView.isVisible = true
                }

                is HistoryStatus.LoadingMore -> {}
                is HistoryStatus.LoadingMoreEnd -> {
                    mBinding.mSmartRefreshLayout.finishLoadMore()
                }

                is HistoryStatus.LoadingMoreFailed -> {
                    mBinding.mSmartRefreshLayout.finishLoadMore(false)
                }

                is HistoryStatus.NoMore -> {
                    mBinding.mSmartRefreshLayout.finishLoadMoreWithNoMoreData()
                }

                is HistoryStatus.CloseSymbolFilter -> {
                    bottomTypesDialog.dismissDialog()
                }

                is HistoryStatus.CloseTimeFilter -> {
                    bottomTimesDialog.dismissDialog()
                }

                else -> {}
            }
        }

        fundingViewModel.historyData.observe(viewLifecycleOwner) {
            positionHistoryAdapter.submitList(it.toMutableList())
        }

        fundingViewModel.selectedType.observe(viewLifecycleOwner) {
            mBinding.tvTypes.text =
                if (it.equals(getString(R.string.all))) getString(R.string.type) else it
        }

        fundingViewModel.timeFilter.observe(viewLifecycleOwner) {
            mBinding.tvTimePeriod.text = it
        }

        lifecycleScope.launch {
            fundingViewModel.statisticsFlow.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED).collect {
                setFundData(it)
                delay(100)
                setFundExpandState(fundExpandState)
            }
        }

        lifecycleScope.launch {
            fundingViewModel.statisticsErrorFlow.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED).collect {
                setFundDataError()
                delay(100)
                setFundExpandState(fundExpandState)
            }
        }
    }

    private fun setFundData(bean: HistoryFundStatisticsBean) {

        if (bean.totalDeposit.mathCompTo("0") != -1) {
            mBinding.tvDeposit.setTextColorDiff(c00c79c)
            mBinding.tvDeposit.setTextDiff(if (bean.totalDeposit.ifNull("0").mathCompTo("0") == 0) "0" else "+${bean.totalDeposit}")
        } else {
            mBinding.tvDeposit.setTextColorDiff(ce35728)
            mBinding.tvDeposit.setTextDiff(bean.totalDeposit.ifNull("0"))
        }

        if (bean.totalWithdraw.mathCompTo("0") != -1) {
            mBinding.tvWithdraw.setTextColorDiff(c00c79c)
            mBinding.tvWithdraw.setTextDiff(if (bean.totalWithdraw.ifNull("0").mathCompTo("0") == 0) "0" else "+${bean.totalWithdraw}")
        } else {
            mBinding.tvWithdraw.setTextColorDiff(ce35728)
            mBinding.tvWithdraw.setTextDiff(bean.totalWithdraw.ifNull("0"))
        }
    }

    private fun setFundDataError() {
        mBinding.tvDeposit.setTextColorDiff(color_c1e1e1e_cebffffff)
        mBinding.tvDeposit.setTextDiff("--")
        mBinding.tvWithdraw.setTextColorDiff(color_c1e1e1e_cebffffff)
        mBinding.tvWithdraw.setTextDiff("--")
    }

    private fun switchPnlExpandState() {
        setFundExpandState(if (fundExpandState == FundExpandState.Collapsed) FundExpandState.Expanded else FundExpandState.Collapsed)
    }

    private fun setFundExpandState(state: FundExpandState) {
        fundExpandState = state
        val (iconRes, expanded) = when (state) {
            FundExpandState.Expanded -> R.drawable.bitmap2_expand_up12x12_c731e1e1e_c61ffffff to true
            FundExpandState.Collapsed -> R.drawable.bitmap2_expand_down12x12_c731e1e1e_c61ffffff to false
        }
        mBinding.ivTriangleDown.setImageResource(iconRes)
        mBinding.clPnl.setExpandAnimation(expanded)
    }

    sealed class FundExpandState {
        data object Expanded : FundExpandState()
        data object Collapsed : FundExpandState()
    }
}
