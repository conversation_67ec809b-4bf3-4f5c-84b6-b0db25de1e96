package cn.com.vau.history.ui

import android.app.Activity
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.FragmentPositionHistoryBinding
import cn.com.vau.databinding.VsLayoutNoDataBinding
import cn.com.vau.history.data.HistoryOrderStatisticsBean
import cn.com.vau.history.data.uiStatus.HistoryStatus
import cn.com.vau.history.data.uiStatus.PositionDialogStatus
import cn.com.vau.history.ui.adapter.BottomTipsListAdapter
import cn.com.vau.history.ui.adapter.HistoryPositionAdapter
import cn.com.vau.history.viewmodel.HistoryPositionViewModel
import cn.com.vau.trade.ext.setExpandAnimation
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.observeState
import cn.com.vau.util.onClickWithDefaultDelegate
import cn.com.vau.util.setTextColorDiff
import cn.com.vau.util.setTextDiff
import cn.com.vau.util.widget.dialog.BottomContentDialog
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class HistoryPositionFragment : BaseMvvmBindingFragment<FragmentPositionHistoryBinding>() {
    private val historyViewModel: HistoryPositionViewModel by viewModels()
    private val positionHistoryAdapter: HistoryPositionAdapter by lazy { HistoryPositionAdapter(historyViewModel) }

    private val bottomSymbolsDialog: BottomSymbolsDialog by lazy {
        BottomSymbolsDialog.Builder(requireContext() as Activity, historyViewModel).build()
    }
    private val bottomTimesDialog: BottomTimeDialog by lazy {
        BottomTimeDialog
            .Builder(
                requireContext() as Activity,
                historyViewModel,
            ).build()
    }

    private val bottomPnlDialog: BottomContentDialog by lazy {
        BottomContentDialog
            .Builder(requireContext() as Activity)
            .setTitle(getString(R.string.closing_pnl))
            .setContent(getString(R.string.the_current_profit_excluding_other_charges))
            .build()
    }

    private val bottomNetPnlDialog: BottomContentDialog by lazy {
        BottomContentDialog
            .Builder(requireContext() as Activity)
            .setTitle(getString(R.string.net_pnl))
            .setContent(getString(R.string.the_profit_and_including_other_charges))
            .build()
    }

    private val bottomChargesDialog: BottomListDialog by lazy {
        val hintList =
            mutableListOf<HintLocalData>().apply {
                add(HintLocalData(getString(R.string.charges), getString(R.string.the_commissions_and_all_the_account)))
                add(HintLocalData(getString(R.string.swap), getString(R.string.the_rollover_interest_either_trading_hours)))
            }
        val statusAdapter = BottomTipsListAdapter(requireContext(), hintList)
        BottomListDialog
            .Builder(requireActivity())
            .setTitle(getString(R.string.charges_swap_dialog_title))
            .setAdapter(statusAdapter)
            .build()
    }

    private var pnlExpandState: PnlExpandState = PnlExpandState.Expanded

    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(requireActivity(), R.attr.color_c1e1e1e_cebffffff) }

    private val c00c79c by lazy { ContextCompat.getColor(requireActivity(), R.color.c00c79c) }

    private val ce35728 by lazy { ContextCompat.getColor(requireActivity(), R.color.ce35728) }

    override fun initView() {
        mBinding.tvSymbols.setOnClickListener {
            bottomSymbolsDialog.showDialog()
        }
        mBinding.ivFilter.onClickWithDefaultDelegate {
            bottomTimesDialog.showDialog()
        }
        mBinding.tvTimePeriod.onClickWithDefaultDelegate {
            switchPnlExpandState()
        }

        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(requireContext())
        mBinding.mRecyclerView.adapter = positionHistoryAdapter
        mBinding.mRecyclerView.itemAnimator = null
        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            historyViewModel.refreshHistoryData()
        }
        mBinding.mSmartRefreshLayout.setOnLoadMoreListener {
            historyViewModel.loadMoreHistoryData()
        }
        mBinding.mEmptyView.setOnInflateListener { _, inflated ->
            val vs = VsLayoutNoDataBinding.bind(inflated)
            vs.mNoDataView.id = R.id.mEmptyView
            vs.mNoDataView.setBackgroundColor(AttrResourceUtil.getColor(requireContext(), R.attr.mainLayoutBg))
            vs.mNoDataView.setIconResource(AttrResourceUtil.getDrawable(requireContext(), R.attr.icNoDataBase))
            vs.mNoDataView.setHintMessage(getString(R.string.no_records_found))
        }
        mBinding.mErrorView.setOnInflateListener { _, inflated ->
            val vs = VsLayoutNoDataBinding.bind(inflated)
            vs.mNoDataView.id = R.id.mErrorView
            vs.mNoDataView.setBackgroundColor(AttrResourceUtil.getColor(requireContext(), R.attr.mainLayoutBg))
            vs.mNoDataView.setIconResource(AttrResourceUtil.getDrawable(requireContext(), R.attr.icNoConnection))
            vs.mNoDataView.setHintMessage(getString(R.string.slow_or_no_internet_connection))
            vs.mNoDataView.setBottomBtnText(getString(R.string.retry))
            vs.mNoDataView.setBottomBtnViewClickListener {
                historyViewModel.refreshHistoryData()
            }
        }
    }

    override fun createObserver() {
        historyViewModel.dialogStatus.run {
            observeState(viewLifecycleOwner, PositionDialogStatus::pnlDialogShow) {
                if (it) bottomPnlDialog.showDialog()
            }
            observeState(viewLifecycleOwner, PositionDialogStatus::netPnlDialogShow) {
                if (it) bottomNetPnlDialog.showDialog()
            }
            observeState(viewLifecycleOwner, PositionDialogStatus::chargesDialogShow) {
                if (it) bottomChargesDialog.showDialog()
            }
        }
        historyViewModel.selectedSymbol.observe(this) {
            mBinding.tvSymbols.text = if (it == "ALL") getString(R.string.symbols) else it
        }

        historyViewModel.timeFilter.observe(viewLifecycleOwner) {
            mBinding.tvTimePeriod.text = it
        }
        historyViewModel.historyStatus.observe(viewLifecycleOwner) {
            when (it) {
                is HistoryStatus.Initial -> {
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = true
                }

                is HistoryStatus.Refreshing -> {
                    mBinding.mSmartRefreshLayout.autoRefreshAnimationOnly()
                    mBinding.mSmartRefreshLayout.resetNoMoreData()
                }

                is HistoryStatus.Empty -> {
                    mBinding.mSmartRefreshLayout.finishRefresh()
                    mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = true
                }

                is HistoryStatus.RefreshingEnd -> {
                    mBinding.mSmartRefreshLayout.finishRefresh()
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = false
                    mBinding.mSmartRefreshLayout.setEnableLoadMore(true)
                    mBinding.mRecyclerView.smoothScrollToPosition(0)
                }

                is HistoryStatus.RefreshingFailed -> {
                    mBinding.mSmartRefreshLayout.finishRefresh(false)
                    mBinding.mErrorView.isVisible = true
                    mBinding.mEmptyView.isVisible = false
                }

                is HistoryStatus.LoadingMore -> {}
                is HistoryStatus.LoadingMoreEnd -> {
                    mBinding.mSmartRefreshLayout.finishLoadMore()
                }

                is HistoryStatus.LoadingMoreFailed -> {
                    mBinding.mSmartRefreshLayout.finishLoadMore(false)
                }

                is HistoryStatus.NoMore -> {
                    mBinding.mSmartRefreshLayout.finishLoadMoreWithNoMoreData()
                }

                is HistoryStatus.CloseSymbolFilter -> {
                    bottomSymbolsDialog.dismissDialog()
                }

                is HistoryStatus.CloseTimeFilter -> {
                    bottomTimesDialog.dismissDialog()
                }

                else -> {}
            }
        }

        historyViewModel.historyData.observe(viewLifecycleOwner) {
            // update history data by diffUtils
            positionHistoryAdapter.submitList(it.toMutableList())
        }

        lifecycleScope.launch {
            historyViewModel.statisticsFlow.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED).collect {
                setPnlData(it)
                delay(100)
                setPnlExpandState(pnlExpandState)
            }
        }

        lifecycleScope.launch {
            historyViewModel.statisticsErrorFlow.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED).collect {
                setPnlDataError()
                delay(100)
                setPnlExpandState(pnlExpandState)
            }
        }
    }

    private fun setPnlData(bean: HistoryOrderStatisticsBean) {

        if (bean.closePnl.mathCompTo("0") != -1) {
            mBinding.tvPnl.setTextColorDiff(c00c79c)
            mBinding.tvPnl.setTextDiff(if (bean.closePnl.ifNull("0").mathCompTo("0") == 0) "0" else "+${bean.closePnl}")
        } else {
            mBinding.tvPnl.setTextColorDiff(ce35728)
            mBinding.tvPnl.setTextDiff(bean.closePnl.ifNull("0"))
        }

        if (bean.netPnl.mathCompTo("0") != -1) {
            mBinding.tvNetPnl.setTextColorDiff(c00c79c)
            mBinding.tvNetPnl.setTextDiff(if (bean.netPnl.ifNull("0").mathCompTo("0") == 0) "0" else "+${bean.netPnl}")
        } else {
            mBinding.tvNetPnl.setTextColorDiff(ce35728)
            mBinding.tvNetPnl.setTextDiff(bean.netPnl.ifNull("0"))
        }
    }

    private fun setPnlDataError() {
        mBinding.tvPnl.setTextColorDiff(color_c1e1e1e_cebffffff)
        mBinding.tvPnl.setTextDiff("--")
        mBinding.tvNetPnl.setTextColorDiff(color_c1e1e1e_cebffffff)
        mBinding.tvNetPnl.setTextDiff("--")
    }

    private fun switchPnlExpandState() {
        setPnlExpandState(if (pnlExpandState == PnlExpandState.Collapsed) PnlExpandState.Expanded else PnlExpandState.Collapsed)
    }

    private fun setPnlExpandState(state: PnlExpandState) {
        pnlExpandState = state
        val (iconRes, expanded) = when (state) {
            PnlExpandState.Expanded -> R.drawable.bitmap2_expand_up12x12_c731e1e1e_c61ffffff to true
            PnlExpandState.Collapsed -> R.drawable.bitmap2_expand_down12x12_c731e1e1e_c61ffffff to false
        }
        mBinding.ivTriangleDown.setImageResource(iconRes)
        mBinding.clPnl.setExpandAnimation(expanded)
    }

    sealed class PnlExpandState {
        data object Expanded : PnlExpandState()
        data object Collapsed : PnlExpandState()
    }

}
