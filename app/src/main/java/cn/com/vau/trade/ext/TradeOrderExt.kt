package cn.com.vau.trade.ext

import android.animation.Animator
import android.animation.ValueAnimator
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.core.content.ContextCompat
import androidx.core.view.isEmpty
import androidx.core.widget.NestedScrollView
import androidx.interpolator.view.animation.FastOutSlowInInterpolator
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import cn.com.vau.R
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareAccountInfoData
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.init.StShareAccountInfoData
import cn.com.vau.trade.data.AccountInfoCardBean
import cn.com.vau.trade.data.AccountInfoType
import cn.com.vau.trade.data.MarginRiskLevel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.mathAdd
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.screenHeight
import androidx.core.view.isVisible

/**
 * 保证金水平字体颜色
 */
fun AccountInfoCardBean.toMarginLevelColor(context: Context): Int {

    val marginLevel = this.marginLevel
    val marginCall = this.marginCall
    val marginStopOut = this.marginStopOut
    val marginCallAddAHundredPercent = this.marginCall.mathAdd("100")
    return if (marginLevel == 0.0) {
        AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
    } else {
        if ("$marginLevel".mathCompTo(marginCall) == 1 && "$marginLevel".mathCompTo(marginCallAddAHundredPercent) != 1) {
            //中风险
            ContextCompat.getColor(context, R.color.ce35728)
        } else if ("$marginLevel".mathCompTo(marginStopOut) == 1 && "$marginLevel".mathCompTo(marginCall) != 1 || "$marginLevel".mathCompTo(marginStopOut) != 1) {
            //高风险 or 爆仓
            ContextCompat.getColor(context, R.color.cf44040)
        } else {
            //低风险
            ContextCompat.getColor(context, R.color.c00c79c)
        }
    }
}

/**
 * 保证金水平风险等级
 */
fun AccountInfoCardBean.toMarginRiskLevel(): MarginRiskLevel {

    val marginLevel = this.marginLevel
    val marginCall = this.marginCall
    val marginStopOut = this.marginStopOut
    val marginCallAddAHundredPercent = this.marginCall.mathAdd("100")
    return if (marginLevel == 0.0) {
        MarginRiskLevel.Low
    } else {
        if ("$marginLevel".mathCompTo(marginCall) == 1 && "$marginLevel".mathCompTo(marginCallAddAHundredPercent) != 1) {
            //中风险
            MarginRiskLevel.Medium
        } else if ("$marginLevel".mathCompTo(marginStopOut) == 1 && "$marginLevel".mathCompTo(marginCall) != 1 || "$marginLevel".mathCompTo(marginStopOut) != 1) {
            //高风险 or 爆仓
            MarginRiskLevel.High
        } else {
            //低风险
            MarginRiskLevel.Low
        }
    }
}

/**
 * 浮动盈亏字体颜色
 */
fun AccountInfoCardBean.toFloatingPnlColor(context: Context): Int {
    return if (profit == 0.0) {
        AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
    } else {
        if (profit > 0) ContextCompat.getColor(context, R.color.c00c79c) else ContextCompat.getColor(context, R.color.cf44040)
    }
}

/**
 * 余额字体颜色
 */
fun AccountInfoCardBean.toBalanceColor(context: Context): Int {
    return if (balance >= 0) AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) else ContextCompat.getColor(context, R.color.cf44040)
}

/**
 * 更新账户信息item值的字体颜色
 */
fun AccountInfoType.toAccountInfoColor(context: Context, cardBean: AccountInfoCardBean): Int {
    return when (this) {
        AccountInfoType.Equity -> {
            AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
        }

        AccountInfoType.FloatingPnL -> {
            cardBean.toFloatingPnlColor(context)
        }

        AccountInfoType.MarginLevel -> {
            // 预付款比率
            cardBean.toMarginLevelColor(context)
        }

        AccountInfoType.Credit -> {
            AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
        }

        AccountInfoType.MarginAndFreeMargin -> {
            AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
        }

        AccountInfoType.Balance -> {
            cardBean.toBalanceColor(context)
        }
    }
}

/**
 * 调整ViewPager2的滑动灵敏度
 */
fun ViewPager2.changeTouchSlop(multiple: Int = 2) {
    try {
        val recyclerViewField = ViewPager2::class.java.getDeclaredField("mRecyclerView")
        recyclerViewField.isAccessible = true
        val recyclerView = recyclerViewField.get(this) as RecyclerView

        val touchSlopField = RecyclerView::class.java.getDeclaredField("mTouchSlop")
        touchSlopField.isAccessible = true
        val touchSlop = touchSlopField.get(recyclerView) as Int
        touchSlopField.set(recyclerView, touchSlop * multiple) // 倍数根据需求调整
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

/**
 * 复制一个账户信息卡片对象
 */
fun ShareAccountInfoData.copyData(): AccountInfoCardBean {
    return AccountInfoCardBean().apply {
        this.equity = <EMAIL>
        this.balance = <EMAIL>
        this.credit = <EMAIL>
        this.margin = <EMAIL>
        this.marginCall = <EMAIL>
        this.marginStopOut = <EMAIL>
        this.profit = <EMAIL>
        this.freeMargin = <EMAIL>
        this.marginLevel = <EMAIL>
    }
}

/**
 * 复制一个账户信息卡片对象
 */
fun StShareAccountInfoData.copyData(): AccountInfoCardBean {
    return AccountInfoCardBean().apply {
        this.profit = <EMAIL>
        this.marginLevel = <EMAIL>
        this.equity = <EMAIL>
        this.balance = <EMAIL>
        this.credit = <EMAIL>
        this.freeMargin = <EMAIL>
        this.margin = <EMAIL>
        this.marginCall = <EMAIL>
        this.marginStopOut = <EMAIL>
    }
}

/**
 * 手数转金额
 */
fun ShareOrderData.getAmountFromVolume(): String {
    val productData = VAUSdkUtil.symbolList().firstOrNull {
        it.symbol == this.symbol
    } ?: return ""
    return OrderUtil.getAmountFromVolume(productData, this.volume ?: "0", if (OrderUtil.isBuyOfOrder(this.cmd)) "1" else "0")
}

fun NestedScrollView.findHasFocusEditText(): View? {
    if (isEmpty()) return null
    val container = getChildAt(0) as? ViewGroup ?: return null
    return findFocusedEditText(container)
}

private fun findFocusedEditText(viewGroup: ViewGroup): EditText? {
    for (i in 0 until viewGroup.childCount) {
        val child = viewGroup.getChildAt(i)
        when {
            child is EditText && child.hasFocus() -> return child
            child is ViewGroup -> findFocusedEditText(child)?.let { return it }
        }
    }
    return null
}

fun View.getTopToScreen(): Int {
    val location = IntArray(2)
    this.getLocationOnScreen(location)
    return location[1]
}

fun View.getBottomToScreen(): Int {
    val topToScreen = getTopToScreen()
    return screenHeight - topToScreen - this.height
}

/**
 * 展开/收起动画扩展方法
 * @param expand true为展开，false为收起
 * @param duration 动画时长，默认300ms
 * @param onAnimationEnd 动画结束回调
 */
fun View.setExpandAnimation(
    expand: Boolean,
    duration: Long = 300L,
    onAnimationEnd: (() -> Unit)? = null
) {
    if (expand) {
        expandWithAnimation(duration, onAnimationEnd)
    } else {
        collapseWithAnimation(duration, onAnimationEnd)
    }
}

/**
 * 展开动画
 * @param duration 动画时长
 * @param onAnimationEnd 动画结束回调
 */
private fun View.expandWithAnimation(
    duration: Long,
    onAnimationEnd: (() -> Unit)? = null
) {
    if (isVisible) {
        onAnimationEnd?.invoke()
        return
    }
    visibility = View.VISIBLE
    alpha = 0f
    val targetHeight = measureViewHeight()
    val originalLayoutParams = layoutParams
    layoutParams = layoutParams.apply { height = 0 }
    val heightAnimator = createHeightAnimator(0, targetHeight, duration)
    val alphaAnimator = createAlphaAnimator(0f, 1f, duration)
    heightAnimator.addListener(object : Animator.AnimatorListener {
        override fun onAnimationStart(animation: Animator) {}
        override fun onAnimationEnd(animation: Animator) {
            layoutParams = originalLayoutParams
            onAnimationEnd?.invoke()
        }

        override fun onAnimationCancel(animation: Animator) {}
        override fun onAnimationRepeat(animation: Animator) {}
    })
    heightAnimator.start()
    alphaAnimator.start()
}

/**
 * 收起动画
 * @param duration 动画时长
 * @param onAnimationEnd 动画结束回调
 */
private fun View.collapseWithAnimation(
    duration: Long,
    onAnimationEnd: (() -> Unit)? = null
) {
    if (!isVisible) {
        onAnimationEnd?.invoke()
        return
    }
    val initialHeight = height
    val originalLayoutParams = layoutParams
    val heightAnimator = createHeightAnimator(initialHeight, 0, duration)
    val alphaAnimator = createAlphaAnimator(1f, 0f, duration)
    heightAnimator.addListener(object : Animator.AnimatorListener {
        override fun onAnimationStart(animation: Animator) {}
        override fun onAnimationEnd(animation: Animator) {
            visibility = View.GONE
            // 恢复原始布局参数
            layoutParams = originalLayoutParams
            onAnimationEnd?.invoke()
        }

        override fun onAnimationCancel(animation: Animator) {}
        override fun onAnimationRepeat(animation: Animator) {}
    })
    heightAnimator.start()
    alphaAnimator.start()
}

/**
 * 测量View的实际高度
 * @return 测量后的高度
 */
private fun View.measureViewHeight(): Int {
    val widthMeasureSpec = View.MeasureSpec.makeMeasureSpec(
        (parent as View).width,
        View.MeasureSpec.EXACTLY
    )
    val heightMeasureSpec = View.MeasureSpec.makeMeasureSpec(
        0,
        View.MeasureSpec.UNSPECIFIED
    )
    measure(widthMeasureSpec, heightMeasureSpec)
    return measuredHeight
}

/**
 * 创建高度动画
 * @param startHeight 起始高度
 * @param endHeight 结束高度
 * @param duration 动画时长
 * @return ValueAnimator
 */
private fun View.createHeightAnimator(
    startHeight: Int,
    endHeight: Int,
    duration: Long
): ValueAnimator {
    return ValueAnimator.ofInt(startHeight, endHeight).apply {
        this.duration = duration
        interpolator = FastOutSlowInInterpolator()
        addUpdateListener { animator ->
            val animatedValue = animator.animatedValue as Int
            val params = layoutParams
            params.height = animatedValue
            layoutParams = params
        }
    }
}

/**
 * 创建透明度动画
 * @param startAlpha 起始透明度
 * @param endAlpha 结束透明度
 * @param duration 动画时长
 * @return ValueAnimator
 */
private fun View.createAlphaAnimator(
    startAlpha: Float,
    endAlpha: Float,
    duration: Long
): ValueAnimator {
    return ValueAnimator.ofFloat(startAlpha, endAlpha).apply {
        this.duration = duration
        interpolator = FastOutSlowInInterpolator()
        addUpdateListener { animator ->
            alpha = animator.animatedValue as Float
        }
    }
}